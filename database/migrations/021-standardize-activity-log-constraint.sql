-- Migration: Standardize activity log event type constraint
-- This migration ensures the activity_log constraint has consistent ordering across all environments

-- Drop the existing check constraint
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;

-- Add the standardized constraint with alphabetical ordering (including benefit events from migration 015)
ALTER TABLE activity_log ADD CONSTRAINT activity_log_event_type_check
CHECK (event_type IN (
  'benefit_added_to_company',
  'benefit_automatically_removed',
  'benefit_disputed',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_cancelled',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_submitted',
  'benefit_removed_from_company',
  'benefit_verified',
  'cache_refresh',
  'company_added',
  'session_cleanup',
  'user_deleted',
  'user_registered'
));

-- Update the comment to reflect all event types in alphabetical order
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, session_cleanup, user_deleted, user_registered';

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('021-standardize-activity-log-constraint', 'Standardize activity log event type constraint ordering across all environments');
