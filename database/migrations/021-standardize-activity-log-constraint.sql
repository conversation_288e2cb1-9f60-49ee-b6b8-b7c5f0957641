-- Migration: Standardize activity log event type constraint
-- This migration ensures the activity_log constraint has consistent ordering across all environments

-- Drop the existing check constraint
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;

-- Add the standardized constraint with alphabetical ordering (including benefit events from migration 015)
-- Use IF NOT EXISTS equivalent by checking if constraint already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'activity_log_event_type_check'
        AND conrelid = 'activity_log'::regclass
    ) THEN
        ALTER TABLE activity_log ADD CONSTRAINT activity_log_event_type_check
        CHECK (event_type IN (
          'benefit_added_to_company',
          'benefit_automatically_removed',
          'benefit_disputed',
          'benefit_removal_dispute_approved',
          'benefit_removal_dispute_cancelled',
          'benefit_removal_dispute_rejected',
          'benefit_removal_dispute_submitted',
          'benefit_removed_from_company',
          'benefit_verified',
          'cache_refresh',
          'company_added',
          'session_cleanup',
          'user_deleted',
          'user_registered'
        ));
    END IF;
END $$;

-- Update the comment to reflect all event types in alphabetical order
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, session_cleanup, user_deleted, user_registered';

-- Log this migration (with ON CONFLICT DO NOTHING to handle re-runs)
INSERT INTO migration_log (migration_name, description)
VALUES ('021-standardize-activity-log-constraint', 'Standardize activity log event type constraint ordering across all environments')
ON CONFLICT (migration_name) DO NOTHING;
