import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logBenefitVerified } from '@/lib/activity-logger'
import { withErrorHandling } from '@/lib/api-error-handler'

async function updateCompanyBenefitStatus(companyBenefitId: string) {
  try {
    // Get all verifications for this company benefit
    const verifications = await query(
      'SELECT status FROM benefit_verifications WHERE company_benefit_id = $1',
      [companyBenefitId]
    )

    const totalVerifications = verifications.rows.length
    const confirmedVerifications = verifications.rows.filter(v => v.status === 'confirmed').length

    // Calculate verification percentage
    const verificationPercentage = totalVerifications > 0 ? (confirmedVerifications / totalVerifications) * 100 : 0

    // Update company benefit verification status
    let isVerified = false
    if (totalVerifications >= 3 && verificationPercentage >= 70) {
      isVerified = true
    }

    await query(
      'UPDATE company_benefits SET is_verified = $1 WHERE id = $2',
      [isVerified, companyBenefitId]
    )

    console.log(`Updated company benefit ${companyBenefitId}: verified=${isVerified}, count=${totalVerifications}`)
  } catch (error) {
    console.error('Error updating company benefit status:', error)
  }
}

async function checkDomainAuthorization(userEmail: string, companyBenefitId: string) {
  try {
    // Get company domain from company_benefit with JOIN
    const result = await query(
      `SELECT cb.company_id, c.domain, c.name
       FROM company_benefits cb
       JOIN companies c ON cb.company_id = c.id
       WHERE cb.id = $1`,
      [companyBenefitId]
    )

    if (result.rows.length === 0) {
      return { authorized: false, message: 'Company benefit not found' }
    }

    const company = result.rows[0]
    if (!company.domain) {
      return {
        authorized: false,
        message: 'Company domain not configured. Contact support to enable benefit verification.',
        companyName: company.name
      }
    }

    // Extract domain from user email
    const userDomain = userEmail.split('@')[1]

    if (userDomain !== company.domain) {
      return {
        authorized: false,
        message: `Employee verification required for ${company.name}`,
        companyName: company.name,
        requiredDomain: company.domain,
        userDomain
      }
    }

    return {
      authorized: true,
      message: 'You are authorized to verify benefits for this company',
      companyName: company.name
    }
  } catch (error) {
    console.error('Error checking domain authorization:', error)
    return { authorized: false, message: 'Authorization check failed' }
  }
}

export const POST = withErrorHandling(async (request: NextRequest) => {
  const userId = await requireAuth()
  const user = await getCurrentUser()

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { companyBenefitId, companyId, benefitId, status, comment, evidence, contactInfo } = body

    // Support legacy test format
    const finalStatus = status || 'confirmed'
    const finalComment = comment || evidence || null

    // Support both formats: direct companyBenefitId or companyId + benefitId
    let finalCompanyBenefitId = companyBenefitId

    if (!finalCompanyBenefitId && companyId && benefitId) {
      // Find or create the company_benefit record
      let companyBenefitResult = await query(
        'SELECT id FROM company_benefits WHERE company_id = $1 AND benefit_id = $2',
        [companyId, benefitId]
      )

      if (companyBenefitResult.rows.length === 0) {
        // Create the company-benefit association if it doesn't exist
        try {
          const createResult = await query(
            `INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)
             VALUES ($1, $2, false, $3)
             RETURNING id`,
            [companyId, benefitId, userId]
          )
          finalCompanyBenefitId = createResult.rows[0].id
        } catch (createError) {
          console.error('Error creating company-benefit association:', createError)
          return NextResponse.json(
            { error: 'Failed to create company benefit association' },
            { status: 500 }
          )
        }
      } else {
        finalCompanyBenefitId = companyBenefitResult.rows[0].id
      }
    }

    if (!finalCompanyBenefitId) {
      return NextResponse.json(
        { error: 'Company benefit ID (or companyId + benefitId) is required' },
        { status: 400 }
      )
    }

    if (finalStatus !== 'confirmed') {
      return NextResponse.json(
        { error: 'Status must be "confirmed"' },
        { status: 400 }
      )
    }

    // Check domain authorization
    const domainAuthResult = await checkDomainAuthorization(user.email, finalCompanyBenefitId)
    if (!domainAuthResult.authorized) {
      return NextResponse.json(
        { error: domainAuthResult.message },
        { status: 403 }
      )
    }

    // Check if user has already verified this benefit
    const existingVerification = await query(
      'SELECT id FROM benefit_verifications WHERE company_benefit_id = $1 AND user_id = $2',
      [finalCompanyBenefitId, userId]
    )

    if (existingVerification.rows.length > 0) {
      return NextResponse.json(
        { error: 'You have already verified this benefit' },
        { status: 409 }
      )
    }

    // Create new verification
    const result = await query(
      `INSERT INTO benefit_verifications (company_benefit_id, user_id, status, comment)
       VALUES ($1, $2, $3, $4)
       RETURNING *`,
      [finalCompanyBenefitId, userId, finalStatus, finalComment]
    )

    const verification = result.rows[0]

    // Get company and benefit details for activity logging
    const detailsResult = await query(
      `SELECT
        c.id as company_id, c.name as company_name,
        b.id as benefit_id, b.name as benefit_name
       FROM company_benefits cb
       JOIN companies c ON cb.company_id = c.id
       JOIN benefits b ON cb.benefit_id = b.id
       WHERE cb.id = $1`,
      [finalCompanyBenefitId]
    )

    if (detailsResult.rows.length > 0) {
      const details = detailsResult.rows[0]
      const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim() || undefined

      // Log the benefit verification activity
      // Log the benefit verification
      await logBenefitVerified(
        details.benefit_id,
        details.benefit_name,
        details.company_id,
        details.company_name,
        userId,
        user.email,
        userName
      )
    }

    // Update company benefit verification status based on verifications
    await updateCompanyBenefitStatus(companyBenefitId)

    return NextResponse.json({
      success: true,
      verificationId: verification.id,
      verification
    }, { status: 201 })
})
