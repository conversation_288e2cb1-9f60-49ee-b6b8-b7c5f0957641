import { NextRequest, NextResponse } from 'next/server'
import { getCompanies, getCompaniesCount, createCompany } from '@/lib/database'
import { requireAuth, getCurrentUser } from '@/lib/auth'
import { logCompanyAdded } from '@/lib/activity-logger'
import { withLocationSearch } from '@/lib/location-middleware'
import { requireWebUIOrAdmin } from '@/lib/api-access-control'
import { logAPIError, withErrorHandling } from '@/lib/api-error-handler'
import type { CompanySize } from '@/types/database'

export async function GET(request: NextRequest) {
  return withLocationSearch(request, async (params) => {
    try {
      // Allow web UI access for all users, block direct API access except for admins
      await requireWebUIOrAdmin(request)

      const filters = {
        location: typeof params.location === 'string' ? params.location : undefined,
        normalizedLocation: params.normalizedLocation || undefined,
        size: typeof params.size === 'string' ? params.size.split(',').filter(Boolean) : undefined,
        industry: typeof params.industry === 'string' ? params.industry.split(',').filter(Boolean) : undefined,
        benefits: typeof params.benefits === 'string' ? params.benefits.split(',').filter(Boolean) : undefined,
        search: typeof params.search === 'string' ? params.search : undefined,
      }

      // Get pagination parameters
      const page = parseInt(params.page as string) || 1
      const limit = parseInt(params.limit as string) || 30  // Load 30 companies by default
      const offset = (page - 1) * limit

      // Get companies with database-level pagination
      const companies = await getCompanies(filters, { limit, offset })

      // Get total count for pagination info
      const total = await getCompaniesCount(filters)

      const response = {
        companies,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }

      return new Response(JSON.stringify(response), {
        headers: { 'Content-Type': 'application/json' }
      })
    } catch (error) {
      if (error instanceof Error) {
        logAPIError(error, request, { operation: 'fetch_companies' })

        // Return appropriate error response
        if (error.message.includes('Direct API access not allowed')) {
          return new Response(
            JSON.stringify({ error: error.message }),
            { status: 403, headers: { 'Content-Type': 'application/json' } }
          )
        }
      }

      return new Response(
        JSON.stringify({ error: 'Failed to fetch companies' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      )
    }
  })
}

export const POST = withErrorHandling(async (request: NextRequest) => {
  const _user = await requireAuth()

    const body = await request.json()
    const { name, size, industry, description, domain, career_url } = body

    if (!name || !size || !industry) {
      return NextResponse.json(
        { error: 'Missing required fields (name, size, industry)' },
        { status: 400 }
      )
    }

    const company = await createCompany({
      name,
      size,
      industry,
      description,
      domain: domain?.toLowerCase(),
      career_url: career_url || '',
    })

    // Get user details for activity logging
    const currentUser = await getCurrentUser()

    // Log the company addition activity
    await logCompanyAdded(
      company.id,
      company.name,
      currentUser?.id,
      currentUser?.email,
      `${currentUser?.firstName || ''} ${currentUser?.lastName || ''}`.trim() || undefined
    )

    return NextResponse.json(company, { status: 201 })
})
