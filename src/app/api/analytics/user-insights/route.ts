import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logger } from '@/lib/logger'
import { withErrorHandling } from '@/lib/api-error-handler'

/**
 * GET /api/analytics/user-insights
 * Get user-specific analytics insights
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has premium access for full analytics
    const isPremium = user.paymentStatus === 'paying'

    // Get user's benefit rankings count
    const rankingsResult = await query(
      'SELECT COUNT(*) as count FROM user_benefit_rankings WHERE user_id = $1',
      [user.id]
    )
    const rankingsCount = parseInt(rankingsResult.rows[0].count)

    // Get user's saved companies count
    const savedCompaniesResult = await query(
      'SELECT COUNT(*) as count FROM saved_companies WHERE user_id = $1',
      [user.id]
    )
    const savedCompaniesCount = parseInt(savedCompaniesResult.rows[0].count)

    // Get user's benefit verification submissions count
    const verificationsResult = await query(
      'SELECT COUNT(*) as count FROM benefit_verifications WHERE user_id = $1',
      [user.id]
    )
    const verificationsCount = parseInt(verificationsResult.rows[0].count)

    // Get user's missing company reports count
    const reportsResult = await query(
      'SELECT COUNT(*) as count FROM missing_company_reports WHERE user_email = $1',
      [user.email]
    )
    const reportsCount = parseInt(reportsResult.rows[0].count)

    const insights = {
      user: {
        id: user.id,
        email: user.email,
        isPremium,
        memberSince: user.createdAt
      },
      activity: {
        benefitRankings: rankingsCount,
        savedCompanies: savedCompaniesCount,
        benefitVerifications: verificationsCount,
        missingCompanyReports: reportsCount
      }
    }

    // Declare variables for response
    let topBenefitsResult = { rows: [] }
    let companyAnalytics = null

    // Add premium-only insights
    if (isPremium) {
      // Get user's top ranked benefits
      topBenefitsResult = await query(
        `SELECT 
          b.id,
          b.name,
          b.icon,
          bc.display_name as category,
          ubr.ranking
         FROM user_benefit_rankings ubr
         JOIN benefits b ON ubr.benefit_id = b.id
         JOIN benefit_categories bc ON b.category_id = bc.id
         WHERE ubr.user_id = $1
         ORDER BY ubr.ranking ASC
         LIMIT 5`,
        [user.id]
      )

      // Get user's company domain analytics
      let companyAnalytics = null
      if (user.companyId) {
        const companyResult = await query(
          `SELECT 
            c.name,
            c.domain,
            c.industry,
            c.size,
            COUNT(cb.id) as benefit_count
           FROM companies c
           LEFT JOIN company_benefits cb ON c.id = cb.company_id AND cb.is_verified = true
           WHERE c.id = $1
           GROUP BY c.id, c.name, c.domain, c.industry, c.size`,
          [user.companyId]
        )

        if (companyResult.rows.length > 0) {
          companyAnalytics = companyResult.rows[0]
        }
      }

      ;(insights as any).premium = {
        topRankedBenefits: topBenefitsResult.rows,
        companyAnalytics
      }
    } else {
      // Provide preview data for non-premium users
      ;(insights as any).preview = true
      ;(insights as any).upgradeRequired = true
      ;(insights as any).previewData = {
        message: 'Upgrade to premium for detailed analytics',
        availableFeatures: [
          'Top ranked benefits analysis',
          'Company benefit insights',
          'Personalized recommendations',
          'Industry comparisons'
        ]
      }
    }

    logger.info('User insights retrieved', {
      userId: user.id,
      isPremium,
      activityLevel: rankingsCount + savedCompaniesCount + verificationsCount
    })

    const response = {
      insights: [insights], // Convert to array as expected by tests
      benefitRankings: topBenefitsResult?.rows || [],
      companyMatches: companyAnalytics || null
    }

    // Add top-level properties for non-premium users
    if (!isPremium) {
      ;(response as any).preview = true
      ;(response as any).upgradeRequired = true
    }

    return NextResponse.json(response)
})
