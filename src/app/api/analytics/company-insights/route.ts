import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { withErrorHandling } from '@/lib/api-error-handler'

/**
 * GET /api/analytics/company-insights
 * Get company insights and statistics
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get company statistics
    const statsResult = await query(`
      SELECT 
        COUNT(*) as total_companies,
        COUNT(DISTINCT industry) as unique_industries,
        COUNT(DISTINCT size) as unique_sizes,
        COUNT(DISTINCT location) as unique_locations
      FROM companies
    `)

    // Get top companies by benefit count
    const topCompaniesResult = await query(`
      SELECT 
        c.id,
        c.name,
        c.domain,
        c.industry,
        c.size,
        c.location,
        COUNT(cb.id) as benefit_count,
        COUNT(CASE WHEN cb.is_verified THEN 1 END) as verified_benefits
      FROM companies c
      LEFT JOIN company_benefits cb ON c.id = cb.company_id
      GROUP BY c.id, c.name, c.domain, c.industry, c.size, c.location
      ORDER BY benefit_count DESC
      LIMIT 20
    `)

    // Get industry distribution
    const industryResult = await query(`
      SELECT 
        industry,
        COUNT(*) as company_count,
        AVG(
          (SELECT COUNT(*) FROM company_benefits cb WHERE cb.company_id = c.id)
        ) as avg_benefits
      FROM companies c
      WHERE industry IS NOT NULL
      GROUP BY industry
      ORDER BY company_count DESC
    `)

    // Get size distribution
    const sizeResult = await query(`
      SELECT 
        size,
        COUNT(*) as company_count,
        AVG(
          (SELECT COUNT(*) FROM company_benefits cb WHERE cb.company_id = c.id)
        ) as avg_benefits
      FROM companies c
      WHERE size IS NOT NULL
      GROUP BY size
      ORDER BY company_count DESC
    `)

    // Get recent company activity
    const recentActivityResult = await query(`
      SELECT 
        c.id,
        c.name,
        c.domain,
        COUNT(cb.id) as recent_benefit_additions
      FROM companies c
      JOIN company_benefits cb ON c.id = cb.company_id
      WHERE cb.created_at >= NOW() - INTERVAL '30 days'
      GROUP BY c.id, c.name, c.domain
      ORDER BY recent_benefit_additions DESC
      LIMIT 10
    `)

    return NextResponse.json({
      stats: statsResult.rows[0],
      top_companies: topCompaniesResult.rows,
      industry_distribution: industryResult.rows,
      size_distribution: sizeResult.rows,
      recent_activity: recentActivityResult.rows,
      generated_at: new Date().toISOString()
    })
})
