import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin } from '@/lib/auth'
import { discoverAndNotifyUsers } from '@/lib/user-discovery'
import { sendEmail } from '@/lib/email'
import { createCompanyDiscoveryEmail } from '@/lib/user-discovery'
import { logCompanyAdded, logCompanyDeleted } from '@/lib/activity-logger'
import { withErrorHandling } from '@/lib/api-error-handler'

export const GET = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')

    const search = searchParams.get('search')
    const sort = searchParams.get('sort') || 'created_at' // 'name', 'created_at'
    
    const offset = (page - 1) * limit
    
    let whereClause = ''
    const params: (string | number)[] = []
    let paramIndex = 1
    
    const conditions: string[] = []

    if (search) {
      conditions.push(`(c.name ILIKE $${paramIndex}
                       OR c.industry ILIKE $${paramIndex}
                       OR EXISTS (
                         SELECT 1 FROM company_locations cl
                         WHERE cl.company_id = c.id
                         AND (cl.city ILIKE $${paramIndex}
                              OR cl.country ILIKE $${paramIndex})
                       ))`)
      params.push(`%${search}%`)
      paramIndex++
    }
    
    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`
    }
    
    // Get companies with benefit counts and user counts
    const companiesQuery = `
      SELECT
        c.*,
        COUNT(DISTINCT cb.id) as benefit_count,
        COUNT(DISTINCT CASE WHEN cb.is_verified = true THEN cb.id END) as verified_benefit_count,
        COUNT(DISTINCT u.id) as total_user_count
      FROM companies c
      LEFT JOIN company_benefits cb ON c.id = cb.company_id
      LEFT JOIN users u ON c.id = u.company_id
      ${whereClause}
      GROUP BY c.id
      ORDER BY ${sort === 'name' ? 'c.name ASC' : 'c.created_at DESC'}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    
    params.push(limit, offset)
    
    const companiesResult = await query(companiesQuery, params)

    // Get locations for each company
    const companyIds = companiesResult.rows.map(c => c.id)
    let companiesWithLocations = companiesResult.rows

    if (companyIds.length > 0) {
      const locationsQuery = `
        SELECT
          cl.company_id,
          json_agg(
            json_build_object(
              'id', cl.id,
              'city', cl.city,
              'country', cl.country,
              'country_code', cl.country_code,
              'latitude', cl.latitude,
              'longitude', cl.longitude,
              'is_primary', cl.is_primary,
              'is_headquarters', cl.is_headquarters,
              'location_type', cl.location_type,
              'location_raw', cl.city || ', ' || cl.country,
              'location_normalized', cl.city || ', ' || cl.country
            ) ORDER BY cl.is_primary DESC, cl.is_headquarters DESC, cl.created_at ASC
          ) as locations
        FROM company_locations cl
        WHERE cl.company_id = ANY($1)
        GROUP BY cl.company_id
      `

      const locationsResult = await query(locationsQuery, [companyIds])
      const locationsMap = new Map()
      locationsResult.rows.forEach(row => {
        locationsMap.set(row.company_id, row.locations)
      })

      // Add locations to companies
      companiesWithLocations = companiesResult.rows.map(company => ({
        ...company,
        locations: locationsMap.get(company.id) || []
      }))
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT c.id) as total
      FROM companies c
      ${whereClause}
    `

    const countResult = await query(countQuery, params.slice(0, -2))
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      companies: companiesWithLocations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
})

export const POST = withErrorHandling(async (request: NextRequest) => {
  const adminUser = await requireAdmin()

    const body = await request.json()
    const { name, industry, size, domain, description, career_url } = body

    if (!name || !industry || !size) {
      return NextResponse.json(
        { error: 'Name, industry, and size are required' },
        { status: 400 }
      )
    }

    const result = await query(
      `INSERT INTO companies (name, industry, size, domain, description, career_url)
       VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
      [name, industry, size, domain || null, description || null, career_url || null]
    )

    const newCompany = result.rows[0]

    // Log the company addition activity
    await logCompanyAdded(
      newCompany.id,
      newCompany.name,
      adminUser.id,
      adminUser.email,
      `${adminUser.firstName || ''} ${adminUser.lastName || ''}`.trim() || undefined
    )

    // Auto-discover and notify existing users if domain is provided
    // This sends "🎉 Company is now on BenefitLens - Verify your benefits!" emails
    let discoveryResult = null
    if (domain) {
      try {
        discoveryResult = await discoverAndNotifyUsers(newCompany.id)
        console.log(`User discovery for ${name}:`, discoveryResult)
      } catch (error) {
        console.error('Error during user discovery:', error)
        // Don't fail company creation if user discovery fails
      }
    }

    // Notify users who reported this company as missing (but exclude those already notified by discovery)
    // This sends "🎉 Company is now available on BenefitLens!" emails to users who specifically reported it
    const notificationResult = { notifiedUsers: 0, errors: [] as string[] }
    if (domain) {
      try {
        // Get list of users already notified by discovery to avoid duplicates
        const discoveredEmails = discoveryResult?.discoveredUsers || []

        // Find pending reports for this domain, excluding users already notified by discovery
        let reportsQuery = 'SELECT id, user_email, first_name, last_name FROM missing_company_reports WHERE email_domain = $1 AND status = $2'
        const reportsParams = [domain.toLowerCase(), 'pending']

        if (discoveredEmails.length > 0) {
          const placeholders = discoveredEmails.map((_, index) => `$${index + 3}`).join(', ')
          reportsQuery += ` AND user_email NOT IN (${placeholders})`
          reportsParams.push(...discoveredEmails)
        }

        const reportsResult = await query(reportsQuery, reportsParams)

        for (const report of reportsResult.rows) {
          try {
            // Send notification email (specific to missing company report)
            const emailOptions = createCompanyDiscoveryEmail(
              report.user_email,
              report.first_name || 'User',
              name,
              domain || 'your-company.com',
              newCompany.id
            )
            await sendEmail(emailOptions)

            // Update report status to 'added' and link to company
            await query(
              'UPDATE missing_company_reports SET status = $1, company_id = $2, updated_at = NOW() WHERE id = $3',
              ['added', newCompany.id, report.id]
            )

            notificationResult.notifiedUsers++
            console.log(`✅ Notified user ${report.user_email} about company addition: ${name}`)
          } catch (error) {
            console.error(`Error notifying user ${report.user_email}:`, error)
            notificationResult.errors.push(`Failed to notify ${report.user_email}`)
          }
        }

        // Update status for reports from users who were already notified by discovery
        if (discoveredEmails.length > 0) {
          try {
            const updateDiscoveredQuery = `
              UPDATE missing_company_reports
              SET status = $1, company_id = $2, updated_at = NOW(), admin_notes = $3
              WHERE email_domain = $4 AND status = $5 AND user_email = ANY($6)
            `
            await query(updateDiscoveredQuery, [
              'added',
              newCompany.id,
              'User was notified via company discovery email',
              domain.toLowerCase(),
              'pending',
              discoveredEmails
            ])
            console.log(`✅ Updated ${discoveredEmails.length} missing company reports for users notified via discovery`)
          } catch (error) {
            console.error('Error updating discovered user reports:', error)
          }
        }

        if (notificationResult.notifiedUsers > 0) {
          console.log(`Notified ${notificationResult.notifiedUsers} users who reported missing company: ${name}`)
        }
      } catch (error) {
        console.error('Error processing missing company reports:', error)
        notificationResult.errors.push('Failed to process missing company reports')
      }
    }

    return NextResponse.json({
      ...newCompany,
      userDiscovery: discoveryResult,
      missingCompanyNotifications: notificationResult
    }, { status: 201 })
})

export const PATCH = withErrorHandling(async (request: NextRequest) => {
  await requireAdmin()
    
    const body = await request.json()
    const { companyId, action, data } = body
    
    if (!companyId || !action) {
      return NextResponse.json(
        { error: 'Company ID and action are required' },
        { status: 400 }
      )
    }
    
    let result
    
    switch (action) {
      case 'update':
        if (!data) {
          return NextResponse.json(
            { error: 'Data is required for update action' },
            { status: 400 }
          )
        }
        
        const keys = Object.keys(data).filter(key => key !== 'updated_at')
        const values = keys.map(key => data[key])
        const setClause = keys.map((key, i) => `${key} = $${i + 1}`).join(', ')

        result = await query(
          `UPDATE companies SET ${setClause}, updated_at = NOW() WHERE id = $${keys.length + 1} RETURNING *`,
          [...values, companyId]
        )
        break
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
    
    return NextResponse.json(result.rows[0])
})

export const DELETE = withErrorHandling(async (request: NextRequest) => {
  const adminUser = await requireAdmin()

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Fetch company details before deletion for logging
    const companyResult = await query(
      'SELECT id, name FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const company = companyResult.rows[0]

    // Delete the company
    await query('DELETE FROM companies WHERE id = $1', [companyId])

    // Log the company deletion activity
    await logCompanyDeleted(
      company.id,
      company.name,
      adminUser.id,
      adminUser.email,
      `${adminUser.firstName || ''} ${adminUser.lastName || ''}`.trim() || undefined
    )

    return NextResponse.json({ success: true })
})
