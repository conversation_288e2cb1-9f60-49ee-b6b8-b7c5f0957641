import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logger } from '@/lib/logger'
import { withErrorHandling } from '@/lib/api-error-handler'

/**
 * POST /api/missing-companies
 * Report a missing company
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { 
      companyName, 
      companyDomain, 
      companyWebsite,
      industry, 
      size,
      location,
      description 
    } = body

    // Validate required fields
    if (!companyName) {
      return NextResponse.json(
        { error: 'Company name is required' },
        { status: 400 }
      )
    }

    // Extract email domain for checking duplicates
    const emailDomain = user.email.split('@')[1]

    // Check if user has already reported this company domain
    if (companyDomain) {
      const existingReport = await query(
        'SELECT id FROM missing_company_reports WHERE user_email = $1 AND email_domain = $2',
        [user.email, emailDomain]
      )

      if (existingReport.rows.length > 0) {
        return NextResponse.json(
          { error: 'You have already reported this company' },
          { status: 409 }
        )
      }
    }

    // Create missing company report using current schema
    const result = await query(
      `INSERT INTO missing_company_reports (
        user_email,
        email_domain,
        first_name,
        last_name,
        status
      ) VALUES ($1, $2, $3, $4, 'pending')
      RETURNING *`,
      [
        user.email,
        emailDomain,
        user.firstName || null,
        user.lastName || null
      ]
    )

    const report = result.rows[0]

    logger.info('Missing company report created', {
      reportId: report.id,
      userId: user.id,
      userEmail: user.email,
      emailDomain
    })

    return NextResponse.json({
      success: true,
      reportId: report.id,
      report: {
        id: report.id,
        userEmail: report.user_email,
        emailDomain: report.email_domain,
        status: report.status,
        createdAt: report.created_at
      }
    }, { status: 201 })
})

/**
 * GET /api/missing-companies
 * Get user's missing company reports
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const result = await query(
      `SELECT
        id,
        user_email,
        email_domain,
        first_name,
        last_name,
        status,
        admin_notes,
        company_id,
        created_at,
        updated_at
       FROM missing_company_reports
       WHERE user_email = $1
       ORDER BY created_at DESC`,
      [user.email]
    )

    return NextResponse.json({
      reports: result.rows
    })
})
