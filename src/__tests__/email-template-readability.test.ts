/**
 * Email Template Readability Tests
 * Tests that email templates are readable and properly formatted
 */

import { describe, it, expect } from 'vitest'
import { createSignInMagicLinkEmail, createSignUpMagicLinkEmail } from '@/lib/magic-link-auth'
import { createCompanyDiscoveryEmail } from '@/lib/user-discovery'
import { createMissingCompanyReportEmail } from '@/lib/email-generators'

describe('Email Template Readability', () => {
  it('should create readable sign-in magic link emails', () => {
    const email = createSignInMagicLinkEmail('<EMAIL>', 'test-token')
    
    // Check basic email structure
    expect(email.to).toBe('<EMAIL>')
    expect(email.subject).toBe('Sign in to BenefitLens')
    expect(email.html).toBeDefined()
    expect(email.text).toBeDefined()
    
    // Check HTML content readability
    expect(email.html).toContain('<!DOCTYPE html>')
    expect(email.html).toContain('<meta charset="utf-8">')
    expect(email.html).toContain('Sign in to BenefitLens')
    expect(email.html).toContain('font-family: Arial, sans-serif')
    expect(email.html).toContain('line-height: 1.6')
    
    // Check responsive design elements
    expect(email.html).toContain('max-width: 600px')
    expect(email.html).toContain('margin: 0 auto')
    
    // Check button styling
    expect(email.html).toContain('background: #2563eb')
    expect(email.html).toContain('color: white')
    expect(email.html).toContain('padding: 12px 24px')
    expect(email.html).toContain('border-radius: 6px')
    
    // Check text version
    expect(email.text).toContain('Sign in to BenefitLens')
    expect(email.text).toContain('This link will expire in 30 minutes')
    
    console.log('✅ Sign-in email template is readable and well-formatted')
  })

  it('should create readable sign-up magic link emails', () => {
    const email = createSignUpMagicLinkEmail('<EMAIL>', 'John', 'test-token')
    
    // Check basic email structure
    expect(email.to).toBe('<EMAIL>')
    expect(email.subject).toBe('Complete your BenefitLens account')
    expect(email.html).toBeDefined()
    expect(email.text).toBeDefined()
    
    // Check personalization
    expect(email.html).toContain('Hello John')
    expect(email.text).toContain('Hello John')
    
    // Check HTML content readability
    expect(email.html).toContain('<!DOCTYPE html>')
    expect(email.html).toContain('<meta charset="utf-8">')
    expect(email.html).toContain('Welcome to BenefitLens')
    expect(email.html).toContain('font-family: Arial, sans-serif')
    
    // Check feature highlights
    expect(email.html).toContain('🏢 Discover companies')
    expect(email.html).toContain('✅ Verify benefits')
    expect(email.html).toContain('💾 Save companies')
    expect(email.html).toContain('📊 Compare benefits')
    
    // Check responsive design
    expect(email.html).toContain('max-width: 600px')
    
    console.log('✅ Sign-up email template is readable and well-formatted')
  })

  it('should create readable company discovery notification emails', () => {
    const email = createCompanyDiscoveryEmail(
      '<EMAIL>',
      'John',
      'Test Company',
      'example.com',
      'company-123'
    )
    
    // Check basic email structure
    expect(email.to).toBe('<EMAIL>')
    expect(email.subject).toContain('Test Company is now available on BenefitLens')
    expect(email.html).toBeDefined()
    expect(email.text).toBeDefined()
    
    // Check personalization
    expect(email.html).toContain('Hello John')
    expect(email.html).toContain('Test Company')
    
    // Check HTML content readability
    expect(email.html).toContain('<!DOCTYPE html>')
    expect(email.html).toContain('<meta charset="utf-8">')
    expect(email.html).toContain('🎉') // Emoji is now in the title
    expect(email.html).toContain('BenefitLens') // Unified header
    
    // Check styling for readability
    expect(email.html).toContain('font-family: Arial, sans-serif')
    expect(email.html).toContain('line-height: 1.6')
    expect(email.html).toContain('max-width: 600px')
    
    // Check action buttons
    expect(email.html).toContain('View Test Company Benefits')
    expect(email.html).toContain('background: linear-gradient')
    expect(email.html).toContain('🏢')
    
    console.log('✅ Company added notification email is readable and well-formatted')
  })

  it('should create readable missing company report emails', () => {
    const email = createMissingCompanyReportEmail(
      'report-123',
      '<EMAIL>',
      'example.com',
      'John',
      'Doe'
    )

    // Check basic email structure
    expect(email.to).toBe(process.env.ADMIN_EMAIL || '<EMAIL>')
    expect(email.subject).toContain('example.com')
    expect(email.html).toBeDefined()
    expect(email.text).toBeDefined()

    // Check content
    expect(email.html).toContain('report-123')
    expect(email.html).toContain('John Doe')
    expect(email.html).toContain('<EMAIL>')
    expect(email.html).toContain('example.com')

    // Check HTML content readability
    expect(email.html).toContain('<!DOCTYPE html>')
    expect(email.html).toContain('<meta charset="utf-8">')

    // Check responsive design
    expect(email.html).toContain('max-width: 600px')
    expect(email.html).toContain('font-family: Arial, sans-serif')

    console.log('✅ Missing company report email is readable and well-formatted')
  })

  it('should have consistent styling across all email templates', () => {
    const signInEmail = createSignInMagicLinkEmail('<EMAIL>', 'test-token')
    const signUpEmail = createSignUpMagicLinkEmail('<EMAIL>', 'John', 'test-token')
    const companyEmail = createCompanyDiscoveryEmail('<EMAIL>', 'John', 'Test Company', 'example.com', 'company-123')
    
    const emails = [signInEmail, signUpEmail, companyEmail]
    
    // Check that all emails use consistent styling
    for (const email of emails) {
      // Check font family consistency
      expect(email.html).toContain('font-family: Arial, sans-serif')
      
      // Check line height consistency
      expect(email.html).toContain('line-height: 1.6')
      
      // Check container width consistency
      expect(email.html).toContain('max-width: 600px')
      
      // Check button styling consistency (flexible matching for different CSS formats)
      expect(email.html).toMatch(/background[^:]*:\s*#2563eb|background-color[^:]*:\s*#2563eb|background:\s*linear-gradient/) // Primary blue color or gradient
      expect(email.html).toContain('color: white')
      expect(email.html).toContain('border-radius')
      
      // Check responsive design
      expect(email.html).toContain('margin: 0 auto')
    }
    
    console.log('✅ All email templates have consistent styling')
  })

  it('should have mobile-friendly email templates', () => {
    const email = createSignInMagicLinkEmail('<EMAIL>', 'test-token')

    // Check mobile-friendly features
    expect(email.html).toContain('<meta charset="utf-8">')
    expect(email.html).toContain('max-width: 600px') // Responsive container
    expect(email.html).toContain('padding: 20px') // Adequate padding

    // Check button sizing for mobile
    expect(email.html).toContain('padding: 12px 24px') // Touch-friendly button size
    expect(email.html).toContain('font-weight: bold') // Clear text

    // Check that links are properly formatted for mobile
    expect(email.html).toContain('word-break: break-all') // Long URLs break properly

    console.log('✅ Email templates are mobile-friendly')
  })

  it('should have Outlook mobile compatible button styling', () => {
    const signInEmail = createSignInMagicLinkEmail('<EMAIL>', 'test-token')
    const signUpEmail = createSignUpMagicLinkEmail('<EMAIL>', 'John', 'test-token')
    const companyEmail = createCompanyDiscoveryEmail('<EMAIL>', 'John', 'Test Company', 'example.com', 'company-123')
    const reportEmail = createMissingCompanyReportEmail('report-123', '<EMAIL>', 'example.com', 'John', 'Doe')

    const emails = [signInEmail, signUpEmail, companyEmail, reportEmail]

    emails.forEach((email, index) => {
      // Check that buttons have proper styling for Outlook mobile compatibility
      // Company discovery email uses inline styles without !important, which is also valid
      const hasColorStyles = email.html.includes('color: #ffffff !important') ||
                             email.html.includes('color: white')
      const hasBackgroundStyles = email.html.includes('!important') ||
                                  email.html.includes('background: linear-gradient') ||
                                  email.html.includes('background-color:')
      const hasTextDecoration = email.html.includes('text-decoration: none !important') ||
                                email.html.includes('text-decoration: none')
      const hasFontWeight = email.html.includes('font-weight: bold !important') ||
                           email.html.includes('font-weight: bold')

      expect(hasColorStyles).toBe(true)
      expect(hasBackgroundStyles).toBe(true)
      expect(hasTextDecoration).toBe(true)
      expect(hasFontWeight).toBe(true)

      console.log(`✅ Email ${index + 1} has Outlook mobile compatible button styling`)
    })

    console.log('✅ All email templates have Outlook mobile compatible button styling')
  })

  it('should have accessible email templates', () => {
    const email = createSignUpMagicLinkEmail('<EMAIL>', 'John', 'test-token')
    
    // Check accessibility features
    expect(email.html).toContain('<!DOCTYPE html>') // Proper HTML structure
    expect(email.html).toContain('<meta charset="utf-8">') // Character encoding
    expect(email.html).toContain('<title>') // Page title
    
    // Check color contrast (basic check)
    expect(email.html).toContain('color: white') // White text on blue background
    expect(email.html).toContain('background: #2563eb') // Blue background
    expect(email.html).toContain('color: #333') // Dark text on light background
    
    // Check that text version is provided
    expect(email.text).toBeDefined()
    expect(email.text?.length).toBeGreaterThan(0)
    
    console.log('✅ Email templates are accessible')
  })

  it('should handle fallback text for email clients that don\'t support HTML', () => {
    const email = createSignInMagicLinkEmail('<EMAIL>', 'test-token')
    
    // Check that text version contains all important information
    expect(email.text).toContain('Sign in to BenefitLens')
    expect(email.text).toContain('Click the link below')
    expect(email.text).toContain('This link will expire in 30 minutes')
    expect(email.text).toContain('http') // Contains the actual link
    
    // Check that text version is readable without HTML formatting
    const lines = email.text?.split('\n').filter(line => line.trim().length > 0) || []
    expect(lines.length).toBeGreaterThan(3) // Multiple paragraphs for readability
    
    console.log('✅ Email templates provide proper text fallbacks')
  })
})
