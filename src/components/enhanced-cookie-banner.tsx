'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, BarChart3, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useConsent } from '@/lib/consent-manager'
import Link from 'next/link'

export function EnhancedCookieBanner() {
  const { hasConsent, preferences, updatePreferences, acceptAll, acceptNecessaryOnly } = useConsent()
  const [showDetails, setShowDetails] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [mounted, setMounted] = useState(false)
  // Local state for preferences while user is making changes
  const [localPreferences, setLocalPreferences] = useState(preferences)

  useEffect(() => {
    setMounted(true)
    setIsVisible(!hasConsent)
  }, [hasConsent])

  // Sync local preferences with global preferences when they change
  useEffect(() => {
    setLocalPreferences(preferences)
  }, [preferences])

  if (!mounted || !isVisible || hasConsent) {
    return null
  }

  const handleAcceptAll = () => {
    acceptAll()
    setIsVisible(false)
  }

  const handleAcceptNecessary = () => {
    acceptNecessaryOnly()
    setIsVisible(false)
  }

  const handleSavePreferences = () => {
    // Save the local preferences to global state
    updatePreferences(localPreferences)
    setIsVisible(false)
  }



  const togglePreference = (key: keyof typeof localPreferences) => {
    if (key === 'necessary') return // Cannot disable necessary cookies

    setLocalPreferences(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t shadow-lg max-h-[80vh] overflow-y-auto">
      <div className="container mx-auto px-4 py-4">
        {!showDetails ? (
          // Simple banner view
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-start gap-3 flex-1">
              <Cookie className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-700">
                <p className="mb-2">
                  <strong>We respect your privacy</strong>
                </p>
                <p>
                  This website uses cookies and similar technologies. Choose your preferences
                  or accept all cookies for the best experience.{' '}
                  <Link href="/datenschutz" className="text-blue-600 hover:text-blue-700 underline">
                    Learn more
                  </Link>
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2 flex-shrink-0 flex-wrap">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDetails(true)}
                className="text-xs sm:text-sm"
              >
                <Settings className="w-3 h-3 mr-1" />
                Settings
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAcceptNecessary}
                className="text-xs sm:text-sm"
              >
                Necessary Only
              </Button>
              <Button
                size="sm"
                onClick={handleAcceptAll}
                className="text-xs sm:text-sm bg-blue-600 hover:bg-blue-700"
              >
                Accept All
              </Button>
            </div>
          </div>
        ) : (
          // Detailed preferences view
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <Cookie className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">Cookie Settings</h3>
            </div>

            <p className="text-sm text-gray-600">
              We use cookies to provide you with the best experience on our website.
              You can adjust your preferences for different cookie categories below.
            </p>

            <div className="space-y-4">
              {/* Necessary Cookies */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4 text-green-600" />
                    <h4 className="font-medium text-gray-900">Necessary Cookies</h4>
                  </div>
                  <div className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                    Always active
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  These cookies are required for the basic functions of the website, including
                  login, security, and session management.
                </p>
              </div>

              {/* Analytics Cookies */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="w-4 h-4 text-blue-600" />
                    <h4 className="font-medium text-gray-900">Analytics Cookies</h4>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={localPreferences.analytics}
                      onChange={() => togglePreference('analytics')}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  Help us understand how visitors interact with the website to improve
                  user experience. All data is anonymized.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                  <p className="text-sm text-blue-800 font-medium mb-2">
                    <Shield className="w-4 h-4 inline mr-1" />
                    IP Address Privacy Notice
                  </p>
                  <p className="text-xs text-blue-700">
                    We collect anonymized IP addresses (last digits removed) for analytics.
                    Full IP addresses are only stored for security purposes.
                    Analytics data with IP addresses is automatically anonymized after 12 months.
                  </p>
                </div>
                <ul className="text-xs text-gray-600 space-y-1 ml-4">
                  <li>• Page views and navigation behavior</li>
                  <li>• Search history and filter usage (with anonymized IP)</li>
                  <li>• Anonymous usage statistics</li>
                  <li>• Performance monitoring</li>
                  <li>• Geographic analytics (region-level only)</li>
                </ul>
              </div>

              {/* Functional Cookies */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4 text-purple-600" />
                    <h4 className="font-medium text-gray-900">Functional Cookies</h4>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={localPreferences.functional}
                      onChange={() => togglePreference('functional')}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
                <p className="text-sm text-gray-600">
                  Enable advanced features like saved preferences,
                  personalized content and improved user experience.
                </p>
              </div>


            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setShowDetails(false)}
                className="flex-1"
              >
                Back
              </Button>
              <Button
                variant="outline"
                onClick={handleAcceptNecessary}
                className="flex-1"
              >
                Necessary Only
              </Button>
              <Button
                onClick={handleSavePreferences}
                className="flex-1 bg-blue-600 hover:bg-blue-700"
              >
                Save Settings
              </Button>
            </div>

            <p className="text-xs text-gray-500 text-center">
              You can change your settings anytime in the{' '}
              <Link href="/privacy-preferences" className="text-blue-600 hover:text-blue-700 underline">
                Privacy Settings
              </Link>.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
