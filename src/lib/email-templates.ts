/**
 * Unified Email Template System
 * Provides consistent header, footer, and styling across all BenefitLens emails
 */

export interface EmailTemplateOptions {
  title: string
  subtitle?: string
  content: string
  recipientEmail: string
  includeFooterLinks?: boolean
}

/**
 * Creates a unified email template with consistent BenefitLens branding
 */
export function createUnifiedEmailTemplate(options: EmailTemplateOptions): string {
  const { title, subtitle, content, recipientEmail, includeFooterLinks = true } = options

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          line-height: 1.6; 
          color: #333; 
          margin: 0; 
          padding: 0; 
          background-color: #f9fafb; 
        }
        .container { 
          max-width: 600px; 
          margin: 0 auto; 
          padding: 20px; 
        }
        .email-wrapper {
          background: white;
          border-radius: 10px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }
        .header { 
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
          color: white; 
          padding: 30px; 
          text-align: center; 
        }
        .header h1 {
          color: white;
          margin: 0;
          font-size: 28px;
          font-weight: bold;
        }
        .header p {
          color: #f0f0f0;
          margin: 10px 0 0 0;
          font-size: 16px;
        }
        .content { 
          padding: 30px; 
          background: white; 
        }
        .button {
          display: inline-block !important;
          background: #2563eb !important;
          color: #ffffff !important;
          padding: 12px 24px !important;
          text-decoration: none !important;
          border-radius: 6px !important;
          margin: 20px 0 !important;
          font-weight: bold !important;
          font-family: Arial, sans-serif !important;
          font-size: 16px !important;
          line-height: 1.4 !important;
          border: none !important;
          text-align: center !important;
        }
        .button:hover {
          background: #1d4ed8 !important;
        }
        .footer { 
          text-align: center; 
          margin-top: 25px; 
          padding-top: 25px;
          border-top: 1px solid #eee;
        }
        .footer p {
          color: #555;
          font-size: 12px;
          margin: 5px 0;
          word-break: break-all; /* Mobile-friendly URL breaking */
        }
        .info-box {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin: 25px 0;
          border-left: 4px solid #2563eb;
        }
        .info-box h3 {
          color: #333;
          margin-top: 0;
          font-size: 18px;
        }
        .highlight-box {
          background: #e3f2fd;
          padding: 20px;
          border-radius: 8px;
          margin: 25px 0;
        }
        .highlight-box h3 {
          color: #1976d2;
          margin-top: 0;
          font-size: 16px;
        }
        @media only screen and (max-width: 600px) {
          .container {
            padding: 10px;
          }
          .content {
            padding: 20px;
          }
          .header {
            padding: 20px;
          }
          .header h1 {
            font-size: 24px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="email-wrapper">
          <div class="header">
            <h1>BenefitLens</h1>
            ${subtitle ? `<p>${subtitle}</p>` : ''}
          </div>
          
          <div class="content">
            ${content}
            
            <div class="footer">
              ${includeFooterLinks ? `
                <p>If you have any questions, please contact our support team.</p>
                <hr style="border: none; border-top: 1px solid #eee; margin: 25px 0;">
              ` : ''}
              
              <p>This email was sent to ${recipientEmail}</p>
              <p>© 2025 BenefitLens - Making workplace benefits transparent</p>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}

/**
 * Creates a button for email templates
 */
export function createEmailButton(url: string, text: string, style?: string): string {
  const buttonStyle = style || 'display: inline-block !important; background: #2563eb !important; color: #ffffff !important; padding: 12px 24px !important; text-decoration: none !important; border-radius: 6px !important; margin: 20px 0 !important; font-weight: bold !important; font-family: Arial, sans-serif !important; font-size: 16px !important; line-height: 1.4 !important; border: none !important;'
  
  return `
    <div style="text-align: center; margin: 25px 0;">
      <a href="${url}" class="button" style="${buttonStyle}">
        ${text}
      </a>
    </div>
  `
}

/**
 * Creates an info box for email templates
 */
export function createInfoBox(title: string, content: string): string {
  return `
    <div class="info-box">
      <h3>${title}</h3>
      ${content}
    </div>
  `
}

/**
 * Creates a highlight box for email templates
 */
export function createHighlightBox(title: string, content: string): string {
  return `
    <div class="highlight-box">
      <h3>${title}</h3>
      ${content}
    </div>
  `
}
